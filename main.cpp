#include <iostream>
#include <vector>
#include <algorithm>
#include <cmath>
using namespace std;

struct Server {
    int npus;
    int speed_coeff;
    int memory;
};

struct User {
    int start_time;
    int end_time;
    int sample_count;
};

int main() {
    ios::sync_with_stdio(false);
    cin.tie(nullptr);
    
    int N;
    cin >> N;
    
    vector<Server> servers(N);
    for (int i = 0; i < N; i++) {
        cin >> servers[i].npus >> servers[i].speed_coeff >> servers[i].memory;
    }
    
    int M;
    cin >> M;
    
    vector<User> users(M);
    for (int i = 0; i < M; i++) {
        cin >> users[i].start_time >> users[i].end_time >> users[i].sample_count;
    }
    
    vector<vector<int>> latency(N, vector<int>(M));
    for (int i = 0; i < N; i++) {
        for (int j = 0; j < M; j++) {
            cin >> latency[i][j];
        }
    }
    
    int a, b;
    cin >> a >> b;
    
    // 为每个用户生成调度方案
    for (int user_id = 0; user_id < M; user_id++) {
        const User& user = users[user_id];
        
        // 找到延迟最小的服务器
        int best_server = 0;
        int min_latency = latency[0][user_id];
        for (int i = 1; i < N; i++) {
            if (latency[i][user_id] < min_latency) {
                min_latency = latency[i][user_id];
                best_server = i;
            }
        }
        
        // 计算最大可用batch size（受显存限制）
        int max_batch = (servers[best_server].memory - b) / a;
        max_batch = min(max_batch, 1000); // 题目约束Bj <= 1000
        max_batch = max(max_batch, 1);    // 至少为1
        
        // 计算需要多少次请求
        int remaining_samples = user.sample_count;
        vector<int> batches;
        
        while (remaining_samples > 0) {
            int batch_size = min(remaining_samples, max_batch);
            batches.push_back(batch_size);
            remaining_samples -= batch_size;
        }
        
        // 输出该用户的方案
        cout << batches.size() << "\n";
        
        int current_time = user.start_time;
        for (int i = 0; i < batches.size(); i++) {
            if (i > 0) cout << " ";
            
            // 输出：time, server, npu, batch_size
            cout << current_time << " " << (best_server + 1) << " " << 1 << " " << batches[i];
            
            // 计算下次发送时间（考虑通信延迟）
            if (i < batches.size() - 1) {
                current_time += latency[best_server][user_id] + 1;
            }
        }
        cout << "\n";
    }
    
    return 0;
}
